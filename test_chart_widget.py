"""
Test script for the chart controls widget
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'dash_app'))

import dash
from dash import html, dcc
import dash_bootstrap_components as dbc

# Import our chart controls widget
from dash_app.widgets.chart_controls import create_chart_controls, get_callbacks

# Create a simple test app
app = dash.Dash(__name__, external_stylesheets=[dbc.themes.BOOTSTRAP])

app.layout = html.Div([
    html.H1("Chart Controls Widget Test", style={"textAlign": "center", "color": "#c9d1d9"}),
    
    # Include LightweightCharts library
    html.Script(src="https://unpkg.com/lightweight-charts@4.2.2/dist/lightweight-charts.standalone.production.js"),
    
    # Include our chart bridge JavaScript
    html.Script(src="/assets/chart_bridge.js"),
    
    # Chart controls widget
    create_chart_controls("test-chart")
    
], style={
    "backgroundColor": "#0e1116",
    "minHeight": "100vh",
    "padding": "20px"
})

# Register callbacks
get_callbacks(app, "test-chart")

if __name__ == '__main__':
    print("Starting test app...")
    print("Chart controls widget components:")
    print("- Symbol input field")
    print("- Timeframe buttons (1m, 3m, 5m, 15m, 30m, 1h, 4h, 1D, 1W)")
    print("- Chart type buttons (Line, Candlestick)")
    print("- Data fetching from Binance API")
    print("- JavaScript bridge for chart updates")
    print("\nOpen http://127.0.0.1:8050 to test the widget")
    
    try:
        app.run_server(debug=True, port=8050)
    except Exception as e:
        print(f"Error running app: {e}")
        print("This might be due to missing dependencies or Python path issues")
