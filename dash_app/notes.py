# Minimal app to test the standalone charting.html
from dash import Dash, html, Input, Output, callback
from dash_extensions import EventListener

# Phase 1:
# Symbol/Timeframe Bar
# Convert to Dash components
# Implement JavaScript bridge for chart updates
# Benefits: Better integration with Dash state management
# Drawing Tools Sidebar
# Convert button layout to Dash
# Maintain JavaScript event handlers
# Benefits: Consistent styling with rest of application

# Phase 2:
# Trade Details Panel
# Convert to Dash components with dynamic content
# Implement event bridge from JavaScript
# Benefits: Better data integration with trading system
# Drawings Panel
# Convert to Dash Offcanvas component
# Implement real-time updates bridge
# Benefits: Better mobile responsiveness

# Phase 3: Polish & Integration
# Styling Harmonization
# Migrate CSS variables to Dash Bootstrap theme
# Ensure consistent theming
# Benefits: Unified design system
# State Management Integration
# Implement comprehensive JavaScript ↔ Dash bridge
# Add persistence for user preferences
# Benefits: Better user experience and data consistency

# https://unpkg.com/lightweight-charts@4.2.2/dist/lightweight-charts.standalone.production.js

# Only include the IFrame page; let the iframe load all its own scripts.
app = Dash(
    __name__,
    # Prevent the charting asset scripts from auto-running in this document;
    # they will still be served under /assets and used by the iframe.
    assets_ignore=r".*\.js$",  # ignore all JS in assets/charting
)

app.layout = html.Div([
    # EventListener to catch custom events from the iframe
    EventListener(
        events=[{"event": "fib_completed", "props": ["detail"]}],
        logging=True,
        id="fib-event-listener"
    ),
    html.Iframe(
        src="/assets/charting/charting.html",  # absolute path to Dash assets
        style={"width": "100%", "height": "100vh", "border": "0"},
        id="chart-iframe"
    ),
    html.Div(
        "Fib completion events will be logged in the console below.",
        style={"color": "#8b949e", "padding": "6px 10px"},
    ),
    # Hidden div to store fib data
    html.Div(id="fib-data-output", style={"display": "none"})
])


@callback(
    Output("fib-data-output", "children"),
    Input("fib-event-listener", "n_events"),
    Input("fib-event-listener", "event")
)
def handle_fib_completion(n_events, event_data):
    if n_events and event_data:
        # Extract the fib data
        fib_data = event_data.get("detail", {})
        fib_type = fib_data.get("type", "manual")

        if fib_type == "auto-fib":
            print("🤖 Auto-Fib Tool Completed!")
            print(f"📈 Swing Type: {fib_data.get('swingType', 'unknown')}")
        else:
            print("🎯 Manual Fib Tool Completed!")

        print("📊 Price Data:", fib_data)

        if fib_data:
            print(f"P1 (Start): {fib_data.get('p1')}")
            print(f"P2 (End): {fib_data.get('p2')}")
            print(f"50% Level: {fib_data.get('fib50')}")
            print(f"61.8% Level: {fib_data.get('fib618')}")
            print(f"78.6% Level: {fib_data.get('fib786')}")
            print(f"TP 23.6%: {fib_data.get('fibtp236')}")
            print(f"TP 38.2%: {fib_data.get('fibtp382')}")
            print(f"Timestamp: {fib_data.get('timestamp')}")
            print("=" * 50)

        return f"Fib completed: {fib_data}"

    return "No fib data yet"


if __name__ == "__main__":
    app.run(debug=False)


def update_btc_candles(_, stored_y_range):
    from services import binance as binance_service

    # Fetch 1-hour interval candlesticks for BTC/USDT
    df = binance_service.get_candlestick_dataframe(symbol="BTCUSDT", limit=1000)
