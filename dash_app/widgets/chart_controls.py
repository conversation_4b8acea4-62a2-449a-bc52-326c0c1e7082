"""
Chart Controls Widget - Symbol/Timeframe Selection for Charting
Replicates the functionality from charting.html symbol-timeframe controls
"""

import dash
from dash import html, dcc, Input, Output, State, callback, clientside_callback
import dash_bootstrap_components as dbc
from typing import List, Dict, Any
import pandas as pd
from binance.client import Client
from services import binance as binance_service


# Available timeframes matching the original charting.html
TIMEFRAMES = [
    {"label": "1m", "value": "1m", "binance_interval": Client.KLINE_INTERVAL_1MINUTE},
    {"label": "3m", "value": "3m", "binance_interval": Client.KLINE_INTERVAL_3MINUTE},
    {"label": "5m", "value": "5m", "binance_interval": Client.KLINE_INTERVAL_5MINUTE},
    {"label": "15m", "value": "15m", "binance_interval": Client.KLINE_INTERVAL_15MINUTE},
    {"label": "30m", "value": "30m", "binance_interval": Client.KLINE_INTERVAL_30MINUTE},
    {"label": "1h", "value": "1h", "binance_interval": Client.KLINE_INTERVAL_1HOUR},
    {"label": "4h", "value": "4h", "binance_interval": Client.KLINE_INTERVAL_4HOUR},
    {"label": "1D", "value": "1d", "binance_interval": Client.KLINE_INTERVAL_1DAY},
    {"label": "1W", "value": "1w", "binance_interval": Client.KLINE_INTERVAL_1WEEK},
]

# Chart types
CHART_TYPES = [
    {"label": "📈", "value": "line", "title": "Line Chart"},
    {"label": "📊", "value": "candlestick", "title": "Candlestick Chart"},
]


def create_chart_controls(chart_id: str = "main-chart") -> html.Div:
    """
    Create the chart controls widget with symbol search, timeframe selection, and chart type toggle.
    
    Args:
        chart_id: Unique identifier for the chart instance
        
    Returns:
        html.Div containing the complete chart controls layout
    """
    
    controls_id = f"{chart_id}-controls"
    
    return html.Div([
        # Store components for state management
        dcc.Store(id=f"{controls_id}-symbol-store", data="BTCUSDT"),
        dcc.Store(id=f"{controls_id}-timeframe-store", data="1h"),
        dcc.Store(id=f"{controls_id}-chart-type-store", data="candlestick"),
        dcc.Store(id=f"{controls_id}-chart-data-store", data=None),
        
        # Symbol/Timeframe Bar
        html.Div([
            # Symbol Search Section
            html.Div([
                html.Span("🔍", style={"marginRight": "8px"}),
                dbc.Input(
                    id=f"{controls_id}-symbol-input",
                    type="text",
                    placeholder="BTCUSDT",
                    value="BTCUSDT",
                    style={
                        "backgroundColor": "#1a1f2e",
                        "border": "1px solid #2b3340",
                        "borderRadius": "6px",
                        "color": "#c9d1d9",
                        "fontSize": "14px",
                        "fontWeight": "600",
                        "minWidth": "120px",
                    },
                    className="symbol-input"
                )
            ], style={
                "display": "flex",
                "alignItems": "center",
                "gap": "8px"
            }, className="symbol-search"),
            
            # Timeframe Buttons
            html.Div([
                dbc.ButtonGroup([
                    dbc.Button(
                        tf["label"],
                        id=f"{controls_id}-timeframe-{tf['value']}",
                        size="sm",
                        outline=True,
                        color="secondary",
                        className="timeframe-btn",
                        style={
                            "minWidth": "40px",
                            "fontSize": "12px",
                            "padding": "4px 8px"
                        }
                    ) for tf in TIMEFRAMES
                ], size="sm")
            ], className="timeframe-buttons"),
            
            # Chart Type Buttons
            html.Div([
                dbc.ButtonGroup([
                    dbc.Button(
                        ct["label"],
                        id=f"{controls_id}-chart-type-{ct['value']}",
                        size="sm",
                        outline=True,
                        color="secondary",
                        title=ct["title"],
                        className="chart-type-btn",
                        style={
                            "minWidth": "40px",
                            "fontSize": "16px",
                            "padding": "4px 8px"
                        }
                    ) for ct in CHART_TYPES
                ])
            ], className="chart-type-buttons")
            
        ], style={
            "display": "flex",
            "alignItems": "center",
            "gap": "20px",
            "padding": "10px 20px",
            "backgroundColor": "#0e1116",
            "borderBottom": "1px solid #2b3340",
            "color": "#c9d1d9"
        }, className="symbol-bar"),
        
        # Chart Container - This will hold the LightweightCharts instance
        html.Div(
            id=f"{controls_id}-chart-container",
            style={
                "width": "100%",
                "height": "calc(100vh - 120px)",  # Adjust based on controls height
                "backgroundColor": "#0e1116"
            }
        )
        
    ], id=f"{controls_id}-wrapper", className="chart-controls-wrapper")


def get_callbacks(app: dash.Dash, chart_id: str = "main-chart") -> None:
    """
    Register all callbacks for the chart controls widget.

    Args:
        app: Dash application instance
        chart_id: Unique identifier for the chart instance
    """

    controls_id = f"{chart_id}-controls"

    # Symbol input callback
    @app.callback(
        Output(f"{controls_id}-symbol-store", "data"),
        Input(f"{controls_id}-symbol-input", "value"),
        prevent_initial_call=True
    )
    def update_symbol(symbol_value):
        """Update symbol when input changes"""
        if symbol_value:
            return symbol_value.upper().strip()
        return "BTCUSDT"

    # Timeframe selection callback - handle all timeframe buttons in one callback
    @app.callback(
        [Output(f"{controls_id}-timeframe-store", "data")] +
        [Output(f"{controls_id}-timeframe-{tf['value']}", "color") for tf in TIMEFRAMES],
        [Input(f"{controls_id}-timeframe-{tf['value']}", "n_clicks") for tf in TIMEFRAMES],
        State(f"{controls_id}-timeframe-store", "data"),
        prevent_initial_call=True
    )
    def update_timeframe(*args):
        """Update timeframe and button states"""
        ctx = dash.callback_context
        if not ctx.triggered:
            return dash.no_update

        # Get which button was clicked
        button_id = ctx.triggered[0]['prop_id'].split('.')[0]
        selected_timeframe = None

        for tf in TIMEFRAMES:
            if f"{controls_id}-timeframe-{tf['value']}" == button_id:
                selected_timeframe = tf['value']
                break

        if selected_timeframe:
            # Update button colors
            colors = []
            for tf in TIMEFRAMES:
                if tf["value"] == selected_timeframe:
                    colors.append("primary")  # Active button
                else:
                    colors.append("secondary")  # Inactive button
            return [selected_timeframe] + colors

        return dash.no_update

    # Chart type selection callback - handle all chart type buttons in one callback
    @app.callback(
        [Output(f"{controls_id}-chart-type-store", "data")] +
        [Output(f"{controls_id}-chart-type-{ct['value']}", "color") for ct in CHART_TYPES],
        [Input(f"{controls_id}-chart-type-{ct['value']}", "n_clicks") for ct in CHART_TYPES],
        State(f"{controls_id}-chart-type-store", "data"),
        prevent_initial_call=True
    )
    def update_chart_type(*args):
        """Update chart type and button states"""
        ctx = dash.callback_context
        if not ctx.triggered:
            return dash.no_update

        # Get which button was clicked
        button_id = ctx.triggered[0]['prop_id'].split('.')[0]
        selected_chart_type = None

        for ct in CHART_TYPES:
            if f"{controls_id}-chart-type-{ct['value']}" == button_id:
                selected_chart_type = ct['value']
                break

        if selected_chart_type:
            # Update button colors
            colors = []
            for ct in CHART_TYPES:
                if ct["value"] == selected_chart_type:
                    colors.append("primary")  # Active button
                else:
                    colors.append("secondary")  # Inactive button
            return [selected_chart_type] + colors

        return dash.no_update

    # Initialize button states on page load
    @app.callback(
        [Output(f"{controls_id}-timeframe-{tf['value']}", "color", allow_duplicate=True) for tf in TIMEFRAMES] +
        [Output(f"{controls_id}-chart-type-{ct['value']}", "color", allow_duplicate=True) for ct in CHART_TYPES],
        Input(f"{controls_id}-wrapper", "id"),
        prevent_initial_call=False
    )
    def initialize_button_states(_):
        """Initialize button states - 1h timeframe and candlestick chart active by default"""
        timeframe_colors = []
        for tf in TIMEFRAMES:
            if tf["value"] == "1h":
                timeframe_colors.append("primary")
            else:
                timeframe_colors.append("secondary")

        chart_type_colors = []
        for ct in CHART_TYPES:
            if ct["value"] == "candlestick":
                chart_type_colors.append("primary")
            else:
                chart_type_colors.append("secondary")

        return timeframe_colors + chart_type_colors

    # Data fetching callback - triggered when symbol or timeframe changes
    @app.callback(
        Output(f"{controls_id}-chart-data-store", "data"),
        [Input(f"{controls_id}-symbol-store", "data"),
         Input(f"{controls_id}-timeframe-store", "data")],
        prevent_initial_call=False
    )
    def fetch_chart_data(symbol, timeframe):
        """Fetch candlestick data from Binance when symbol or timeframe changes"""
        if not symbol or not timeframe:
            return None

        try:
            # Find the corresponding Binance interval
            binance_interval = Client.KLINE_INTERVAL_1HOUR  # Default
            for tf in TIMEFRAMES:
                if tf["value"] == timeframe:
                    binance_interval = tf["binance_interval"]
                    break

            # Fetch data from Binance
            df = binance_service.get_candlestick_dataframe(
                symbol=symbol,
                interval=binance_interval,
                limit=1000
            )

            if df.empty:
                return None

            # Convert DataFrame to format expected by LightweightCharts
            chart_data = []
            for index, row in df.iterrows():
                # Convert timestamp to Unix timestamp (seconds)
                timestamp = int(index.timestamp())

                chart_data.append({
                    "time": timestamp,
                    "open": float(row["open"]),
                    "high": float(row["high"]),
                    "low": float(row["low"]),
                    "close": float(row["close"]),
                    "volume": float(row["volume"])
                })

            return {
                "data": chart_data,
                "symbol": symbol,
                "timeframe": timeframe,
                "timestamp": pd.Timestamp.now().isoformat()
            }

        except Exception as e:
            print(f"Error fetching chart data for {symbol} @ {timeframe}: {e}")
            return None

    # Clientside callback to initialize chart when container is ready
    app.clientside_callback(
        f"""
        function(container_id) {{
            if (!container_id) return window.dash_clientside.no_update;

            // Wait for LightweightCharts to be available
            if (typeof LightweightCharts === 'undefined') {{
                setTimeout(function() {{
                    if (window.chartBridge && window.chartBridge.initChart) {{
                        window.chartBridge.initChart(container_id, '{chart_id}');
                    }}
                }}, 100);
                return window.dash_clientside.no_update;
            }}

            // Initialize chart
            if (window.chartBridge && window.chartBridge.initChart) {{
                window.chartBridge.initChart(container_id, '{chart_id}');
            }}

            return window.dash_clientside.no_update;
        }}
        """,
        Output(f"{controls_id}-chart-container", "style", allow_duplicate=True),
        Input(f"{controls_id}-chart-container", "id"),
        prevent_initial_call=False
    )

    # Clientside callback to update chart when data changes
    app.clientside_callback(
        f"""
        function(chart_data, chart_type) {{
            if (!chart_data || !chart_data.data) {{
                return window.dash_clientside.no_update;
            }}

            // Wait for chart bridge to be available
            if (!window.chartBridge || !window.chartBridge.updateChartData) {{
                setTimeout(function() {{
                    if (window.chartBridge && window.chartBridge.updateChartData) {{
                        window.chartBridge.updateChartData('{chart_id}', chart_data.data, chart_type);
                    }}
                }}, 100);
                return window.dash_clientside.no_update;
            }}

            // Update chart data
            window.chartBridge.updateChartData('{chart_id}', chart_data.data, chart_type);

            return window.dash_clientside.no_update;
        }}
        """,
        Output(f"{controls_id}-chart-container", "className", allow_duplicate=True),
        [Input(f"{controls_id}-chart-data-store", "data"),
         Input(f"{controls_id}-chart-type-store", "data")],
        prevent_initial_call=False
    )


def get_timeframe_binance_interval(timeframe: str) -> str:
    """
    Convert timeframe value to Binance interval constant.

    Args:
        timeframe: Timeframe value (e.g., "1h", "1d")

    Returns:
        Binance interval constant
    """
    for tf in TIMEFRAMES:
        if tf["value"] == timeframe:
            return tf["binance_interval"]
    return Client.KLINE_INTERVAL_1HOUR  # Default


# CSS styles to match the original charting.html appearance
CHART_CONTROLS_CSS = """
.chart-controls-wrapper {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
}

.symbol-bar {
    background: #0e1116 !important;
    border-bottom: 1px solid #2b3340 !important;
}

.symbol-input:focus {
    border-color: #58a6ff !important;
    box-shadow: 0 0 0 2px rgba(88, 166, 255, 0.2) !important;
}

.timeframe-btn, .chart-type-btn {
    transition: all 0.2s ease !important;
}

.timeframe-btn:hover, .chart-type-btn:hover {
    background-color: #2b3340 !important;
}
"""
