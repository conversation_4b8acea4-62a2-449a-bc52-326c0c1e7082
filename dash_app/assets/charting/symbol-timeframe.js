// Symbol and Timeframe management
// This file handles symbol search, timeframe selection, and chart type switching

// ----- Elements -----
const symbolInput = document.getElementById('symbol-input');
const timeframeBtns = document.querySelectorAll('.timeframe-btn');
const lineChartBtn = document.getElementById('line-chart-btn');
const candleChartBtn = document.getElementById('candle-chart-btn');

// ----- Current State -----
let currentSymbol = 'BTCUSDT';
let currentTimeframe = '1h';
let currentChartType = 'candlestick'; // 'candlestick' or 'line'

// ----- Symbol Input Handler -----
symbolInput.addEventListener('input', (e) => {
  currentSymbol = e.target.value.toUpperCase();
  // TODO: Implement symbol validation and data fetching
  console.log('Symbol changed to:', currentSymbol);
});

symbolInput.addEventListener('keypress', (e) => {
  if (e.key === 'Enter') {
    loadSymbolData(currentSymbol, currentTimeframe);
  }
});

// ----- Timeframe Button Handlers -----
timeframeBtns.forEach(btn => {
  btn.addEventListener('click', () => {
    // Remove active class from all buttons
    timeframeBtns.forEach(b => b.classList.remove('active'));
    
    // Add active class to clicked button
    btn.classList.add('active');
    
    // Update current timeframe
    currentTimeframe = btn.dataset.timeframe;
    
    console.log('Timeframe changed to:', currentTimeframe);
    loadSymbolData(currentSymbol, currentTimeframe);
  });
});

// ----- Chart Type Button Handlers -----
lineChartBtn.addEventListener('click', () => {
  if (currentChartType !== 'line') {
    currentChartType = 'line';
    lineChartBtn.classList.add('active');
    candleChartBtn.classList.remove('active');
    
    console.log('Chart type changed to: line');
    switchChartType('line');
  }
});

candleChartBtn.addEventListener('click', () => {
  if (currentChartType !== 'candlestick') {
    currentChartType = 'candlestick';
    candleChartBtn.classList.add('active');
    lineChartBtn.classList.remove('active');
    
    console.log('Chart type changed to: candlestick');
    switchChartType('candlestick');
  }
});

// ----- Data Loading Function -----
function loadSymbolData(symbol, timeframe) {
  console.log(`Loading data for ${symbol} on ${timeframe} timeframe`);
  
  // TODO: Implement actual data fetching from API
  // For now, just generate new demo data
  generateDemoData(symbol, timeframe);
}

// ----- Chart Type Switching -----
function switchChartType(type) {
  if (!chart || !series) {
    console.error('Chart or series not initialized');
    return;
  }
  
  try {
    // Remove current series
    chart.removeSeries(series);
    
    // Create new series based on type
    if (type === 'line') {
      window.series = chart.addLineSeries({
        color: '#58a6ff',
        lineWidth: 2,
        priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
      });
      
      // Convert candlestick data to line data (using close prices)
      const lineData = window.currentData ? window.currentData.map(candle => ({
        time: candle.time,
        value: candle.close
      })) : [];
      
      series.setData(lineData);
    } else {
      window.series = chart.addCandlestickSeries({
        upColor: '#26a69a',
        downColor: '#ef5350',
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
        borderVisible: false,
        priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
      });
      
      // Use stored candlestick data
      if (window.currentData) {
        series.setData(window.currentData);
      }
    }
    
    console.log(`Chart type switched to: ${type}`);
  } catch (error) {
    console.error('Error switching chart type:', error);
  }
}

// ----- Demo Data Generation -----
function generateDemoData(symbol, timeframe) {
  console.log(`Generating demo data for ${symbol} (${timeframe})`);
  
  // Generate new demo data
  const data = [];
  const t0 = Math.floor(Date.now()/1000) - 3600*24*30; // 30 days ago
  let p = 100 + Math.random() * 50; // Random starting price
  
  for (let i = 0; i < 500; i++) {
    const t = t0 + i * 3600; // Hourly data
    const o = p;
    const h = o + Math.random() * 3 + 1;
    const l = o - (Math.random() * 3 + 1);
    const c = l + Math.random() * (h - l);
    p = c;
    data.push({ time: t, open: o, high: h, low: l, close: c });
  }
  
  // Add whitespace points for future drawing
  const lastTs = data[data.length - 1].time;
  const candleInterval = data.length > 1 ? data[1].time - data[0].time : 3600;
  const numWhiteSpaces = 200;
  
  for (let i = 1; i <= numWhiteSpaces; i++) {
    data.push({ time: lastTs + (candleInterval * i) });
  }
  
  // Store current data globally
  window.currentData = data;
  
  // Update chart with new data
  if (series) {
    if (currentChartType === 'line') {
      const lineData = data.filter(d => d.close).map(candle => ({
        time: candle.time,
        value: candle.close
      }));
      series.setData(lineData);
    } else {
      series.setData(data);
    }
    
    // Set visible range to show some future space
    const futureTime = lastTs + (candleInterval * 7);
    chart.timeScale().setVisibleRange({
      from: data[Math.max(0, data.length - numWhiteSpaces - 100)].time,
      to: futureTime
    });
  }
}

// ----- Initialize -----
// Set initial symbol in input
symbolInput.value = currentSymbol;

// Set initial active timeframe button
document.querySelector(`[data-timeframe="${currentTimeframe}"]`)?.classList.add('active');

// Set initial chart type
if (currentChartType === 'candlestick') {
  candleChartBtn.classList.add('active');
} else {
  lineChartBtn.classList.add('active');
}

console.log('Symbol/Timeframe controls initialized');
