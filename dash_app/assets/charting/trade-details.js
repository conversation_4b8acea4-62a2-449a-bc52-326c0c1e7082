// Trade details generation - extracted from GOOD CHART.html
// This file contains trade plan generation and display functions

// ----- Trade Details Generation -----
function generateTradeDetails(p1, p2, fib50, fib618, fib786, fibtp236, fibtp382) {
  console.log('generateTradeDetails called with:', { p1, p2, fib50, fib618, fib786, fibtp236, fibtp382 });
  console.log('tradeInfoEl:', tradeInfoEl);

  if (!tradeInfoEl) {
    console.error('tradeInfoEl not found!');
    return;
  }

  // Clear any existing trade details first
  tradeInfoEl.innerHTML = '<div class="muted">Generating trade plan...</div>';

  // Determine if P1 is low and P2 is high (bullish setup)
  const isLowToHigh = p1 < p2;
  const accountSize = 10000;

  if (isLowToHigh) {
    // LONG: P1 (low) = Stop Loss, retracements = entries, extensions = take profits
    const stopLoss = p1;
    const entries = [
      { level: '50%', price: fib50, qty: 2 },
      { level: '61.8%', price: fib618, qty: 2 },
      { level: '78.6%', price: fib786, qty: 2 }
    ];
    const takeProfits = [
      { level: '23.6%', price: fibtp236, qty: 3, note: 'Half position' },
      { level: '38.2%', price: fibtp382, qty: 3, note: 'Remaining position' }
    ];

    displayTradeDetails('LONG', stopLoss, entries, takeProfits, accountSize);
  } else {
    // SHORT: P1 (high) = Stop Loss, retracements = entries, extensions = take profits
    const stopLoss = p1;
    const entries = [
      { level: '50%', price: fib50, qty: 2 },
      { level: '61.8%', price: fib618, qty: 2 },
      { level: '78.6%', price: fib786, qty: 2 }
    ];
    const takeProfits = [
      { level: '23.6%', price: fibtp236, qty: 3, note: 'Half position' },
      { level: '38.2%', price: fibtp382, qty: 3, note: 'Remaining position' }
    ];

    displayTradeDetails('SHORT', stopLoss, entries, takeProfits, accountSize);
  }
}

function displayTradeDetails(direction, stopLoss, entries, takeProfits, accountSize) {
  // Calculate total position size and risk
  const totalQty = entries.reduce((sum, entry) => sum + entry.qty, 0);
  const avgEntryPrice = entries.reduce((sum, entry) => sum + (entry.price * entry.qty), 0) / totalQty;

  // Calculate risk per share and total risk
  const riskPerShare = Math.abs(avgEntryPrice - stopLoss);
  const totalRisk = riskPerShare * totalQty;
  const accountRisk = (totalRisk / accountSize) * 100;

  // Calculate potential profits
  const tp1Profit = Math.abs(takeProfits[0].price - avgEntryPrice) * takeProfits[0].qty;
  const tp2Profit = Math.abs(takeProfits[1].price - avgEntryPrice) * takeProfits[1].qty;
  const totalPotentialProfit = tp1Profit + tp2Profit;
  const riskRewardRatio = totalPotentialProfit / totalRisk;

  // New horizontal layout for bottom panel
  let html = `
    <div class="trade-plan-header">
      <div class="trade-plan-title" style="color: ${direction === 'LONG' ? '#28a745' : '#dc3545'};">
        🎯 ${direction} Trade Plan
      </div>
      <div class="trade-plan-summary">
        <span>💰 Risk: ${accountRisk.toFixed(2)}% ($${totalRisk.toFixed(2)})</span>
        <span>📊 R/R: 1:${riskRewardRatio.toFixed(2)}</span>
        <span>💵 Potential: $${totalPotentialProfit.toFixed(2)}</span>
      </div>
    </div>

    <div class="trade-levels">
      <div class="trade-section">
        <div class="trade-section-title">🛑 STOP LOSS</div>
        <div class="trade-entry stop-loss">
          <span>${stopLoss.toFixed(2)}</span>
          <span>Risk: $${totalRisk.toFixed(2)}</span>
        </div>
      </div>

      <div class="trade-section">
        <div class="trade-section-title">📈 ENTRIES (Total Qty: ${totalQty})</div>
        ${entries.map(entry => {
          const entryValue = entry.price * entry.qty;
          return `<div class="trade-entry entry">
            <span>${entry.level}: ${entry.price.toFixed(2)} (${entry.qty})</span>
            <span>$${entryValue.toFixed(2)}</span>
          </div>`;
        }).join('')}
      </div>

      <div class="trade-section">
        <div class="trade-section-title">🎯 TAKE PROFITS</div>
        ${takeProfits.map(tp => {
          const profit = Math.abs(tp.price - avgEntryPrice) * tp.qty;
          return `<div class="trade-entry take-profit">
            <span>${tp.level}: ${tp.price.toFixed(2)} (${tp.qty})</span>
            <span>+$${profit.toFixed(2)}</span>
          </div>`;
        }).join('')}
      </div>
    </div>
  `;

  tradeInfoEl.innerHTML = html;

  // Show the bottom panel
  const tradePanel = document.getElementById('trade-details-panel');
  if (tradePanel) {
    tradePanel.classList.add('show');
  }
}
