// Enhanced line dragging functionality with 2D movement and trendline endpoint dragging
// This file adds the ability to drag horizontal lines and trendline endpoints

// Dragging state variables
let isDragging = false;
let draggedLine = null;
let draggedTrendlineEndpoint = null;
let draggedRectangleCorner = null;
let dragStartY = null;
let dragStartX = null;
let dragThreshold = 5; // pixels - how close mouse needs to be to a line to start dragging

// Function to find the closest line to a given Y coordinate
function findClosestLine(mouseY) {
    let closestLine = null;
    let minDistance = dragThreshold;
    
    for (const line of lines) {
        // Only consider single lines and fibonacci levels that can be dragged
        if (line.kind === "single" || line.kind === "fib1" || line.kind === "fib2" || 
            line.kind === "fib50" || line.kind === "fib618" || line.kind === "fib786" ||
            line.kind === "fibtp236" || line.kind === "fibtp382") {
            
            // Convert line price to screen Y coordinate
            const lineY = series.priceToCoordinate(line.price);
            if (lineY !== null) {
                const distance = Math.abs(mouseY - lineY);
                if (distance < minDistance) {
                    minDistance = distance;
                    closestLine = line;
                }
            }
        }
    }
    
    return closestLine;
}

// Function to find the closest trendline endpoint to mouse position
function findClosestTrendlineEndpoint(mouseX, mouseY) {
    let closestEndpoint = null;
    let minDistance = dragThreshold * 2; // Slightly larger threshold for endpoints
    
    for (const line of lines) {
        if (line.kind === "trendline" && line.t1 && line.t2 && line.p1 && line.p2) {
            // Convert trendline endpoints to screen coordinates
            const startX = chart.timeScale().timeToCoordinate(line.t1);
            const startY = series.priceToCoordinate(line.p1);
            const endX = chart.timeScale().timeToCoordinate(line.t2);
            const endY = series.priceToCoordinate(line.p2);
            
            if (startX !== null && startY !== null) {
                const startDistance = Math.sqrt(Math.pow(mouseX - startX, 2) + Math.pow(mouseY - startY, 2));
                if (startDistance < minDistance) {
                    minDistance = startDistance;
                    closestEndpoint = { line, endpoint: "start", x: startX, y: startY };
                }
            }
            
            if (endX !== null && endY !== null) {
                const endDistance = Math.sqrt(Math.pow(mouseX - endX, 2) + Math.pow(mouseY - endY, 2));
                if (endDistance < minDistance) {
                    minDistance = endDistance;
                    closestEndpoint = { line, endpoint: "end", x: endX, y: endY };
                }
            }
        }
    }
    
    return closestEndpoint;
}

// Function to recreate rectangle with updated dimensions
function recreateRectangle(rectLine) {
    // Remove old rectangle
    if (rectLine.handle) {
        try {
            chart.removeSeries(rectLine.handle);
        } catch(e) {
            console.error("Error removing old rectangle:", e);
        }
    }

    // Create new rectangle with updated corners
    const rectangleData = [
        { time: rectLine.startTime, value: rectLine.topPrice },    // Top-left
        { time: rectLine.endTime, value: rectLine.topPrice },      // Top-right
        { time: rectLine.endTime, value: rectLine.bottomPrice },   // Bottom-right
        { time: rectLine.startTime, value: rectLine.bottomPrice }, // Bottom-left
        { time: rectLine.startTime, value: rectLine.topPrice }     // Close the rectangle
    ];

    rectLine.handle = chart.addLineSeries({
        color: '#58a6ff',
        lineWidth: 2,
        priceLineVisible: false,
        lastValueVisible: false,
        title: 'Rectangle'
    });

    rectLine.handle.setData(rectangleData);

    // Update list item if it exists
    const listItem = document.getElementById("li-" + rectLine.id);
    if (listItem) {
        const pill = listItem.querySelector(".pill");
        if (pill) {
            pill.textContent = `${fmt(rectLine.topPrice)} → ${fmt(rectLine.bottomPrice)}`;
        }
    }
}

// Function to calculate time from mouse position (for whitespace areas)
function calculateTimeFromPosition(mouseX) {
    const visibleRange = chart.timeScale().getVisibleRange();
    if (visibleRange) {
        const chartWidth = document.getElementById("chart").clientWidth;
        const totalTimeRange = visibleRange.to - visibleRange.from;
        const mouseRatio = mouseX / chartWidth;
        return visibleRange.from + (totalTimeRange * mouseRatio);
    }
    return null;
}

// Function to find the closest rectangle corner to mouse position
function findClosestRectangleCorner(mouseX, mouseY) {
    let closestCorner = null;
    let minDistance = dragThreshold * 2; // Slightly larger threshold for corners

    for (const line of lines) {
        if (line.kind === "rectangle" && line.startTime && line.endTime && line.topPrice && line.bottomPrice) {
            // Convert all 4 corners to screen coordinates
            const corners = [
                {
                    name: "top-left",
                    x: chart.timeScale().timeToCoordinate(line.startTime),
                    y: series.priceToCoordinate(line.topPrice),
                    cursor: "nw-resize"
                },
                {
                    name: "top-right",
                    x: chart.timeScale().timeToCoordinate(line.endTime),
                    y: series.priceToCoordinate(line.topPrice),
                    cursor: "ne-resize"
                },
                {
                    name: "bottom-left",
                    x: chart.timeScale().timeToCoordinate(line.startTime),
                    y: series.priceToCoordinate(line.bottomPrice),
                    cursor: "sw-resize"
                },
                {
                    name: "bottom-right",
                    x: chart.timeScale().timeToCoordinate(line.endTime),
                    y: series.priceToCoordinate(line.bottomPrice),
                    cursor: "se-resize"
                }
            ];

            // Check distance to each corner
            corners.forEach(corner => {
                if (corner.x !== null && corner.y !== null) {
                    const distance = Math.sqrt(Math.pow(mouseX - corner.x, 2) + Math.pow(mouseY - corner.y, 2));
                    if (distance < minDistance) {
                        minDistance = distance;
                        closestCorner = { line, corner: corner.name, x: corner.x, y: corner.y, cursor: corner.cursor };
                    }
                }
            });
        }
    }

    return closestCorner;
}

// Function to update cursor style when hovering over draggable elements
function updateCursor(mouseX, mouseY) {
    const chartContainer = document.getElementById("chart");

    if (isDragging) return; // Do not change cursor while dragging

    // Check for rectangle corners first (highest priority)
    const closestCorner = findClosestRectangleCorner(mouseX, mouseY);
    if (closestCorner) {
        chartContainer.style.cursor = closestCorner.cursor;
        const { line, corner } = closestCorner;
        // Ensure built-in series dot is off (covers rectangles created before code change)
        try { if (line.handle?.applyOptions) line.handle.applyOptions({ crosshairMarkerVisible: false }); } catch(_) {}
        // Show our helper dot at the exact corner being hovered
        switch (corner) {
          case 'top-left':
            showCornerDotAt(line.startTime, line.topPrice);
            break;
          case 'top-right':
            showCornerDotAt(line.endTime, line.topPrice);
            break;
          case 'bottom-left':
            showCornerDotAt(line.startTime, line.bottomPrice);
            break;
          case 'bottom-right':
            showCornerDotAt(line.endTime, line.bottomPrice);
            break;
        }
        return;
    }
    hideCornerDot();

    // Check for trendline endpoints second
    const closestEndpoint = findClosestTrendlineEndpoint(mouseX, mouseY);
    if (closestEndpoint) {
        chartContainer.style.cursor = "crosshair";
        return;
    }

    // Check for horizontal lines
    const closestLine = findClosestLine(mouseY);
    if (closestLine) {
        chartContainer.style.cursor = "move";
        return;
    }

    // Default cursor
    chartContainer.style.cursor = "default";
}

// Function to start dragging a line, trendline endpoint, or rectangle corner
function startDrag(mouseX, mouseY) {
    if (mode !== "normal") return false; // Only allow dragging in normal mode

    // Check for rectangle corners first (highest priority)
    const closestCorner = findClosestRectangleCorner(mouseX, mouseY);
    if (closestCorner) {
        hideCornerDot();
        isDragging = true;
        draggedRectangleCorner = closestCorner;
        draggedTrendlineEndpoint = null;
        draggedLine = null;
        dragStartY = mouseY;
        dragStartX = mouseX;

        // Disable chart pan/zoom while dragging
        setPanZoomEnabled(false);

        // Change cursor to indicate dragging
        document.getElementById("chart").style.cursor = closestCorner.cursor;

        return true;
    }

    // Check for trendline endpoints second
    const closestEndpoint = findClosestTrendlineEndpoint(mouseX, mouseY);
    if (closestEndpoint) {
        isDragging = true;
        draggedTrendlineEndpoint = closestEndpoint;
        draggedRectangleCorner = null;
        draggedLine = null;
        dragStartY = mouseY;
        dragStartX = mouseX;

        // Disable chart pan/zoom while dragging
        setPanZoomEnabled(false);

        // Change cursor to indicate dragging
        document.getElementById("chart").style.cursor = "crosshair";

        return true;
    }

    // Check for horizontal lines
    const closestLine = findClosestLine(mouseY);
    if (closestLine) {
        isDragging = true;
        draggedLine = closestLine;
        draggedTrendlineEndpoint = null;
        draggedRectangleCorner = null;
        dragStartY = mouseY;
        dragStartX = mouseX;

        // Disable chart pan/zoom while dragging
        setPanZoomEnabled(false);

        // Change cursor to indicate dragging
        document.getElementById("chart").style.cursor = "move";

        return true;
    }

    return false;
}

// Function to update trendline endpoint position during drag
function updateTrendlineEndpointDrag(mouseX, mouseY) {
    if (!draggedTrendlineEndpoint) return;
    
    const { line, endpoint } = draggedTrendlineEndpoint;
    
    // Calculate new price and time based on mouse position
    const newPrice = snap(series.coordinateToPrice(mouseY));
    if (isNaN(newPrice)) return;
    
    let newTime = chart.timeScale().coordinateToTime(mouseX);
    
    // Handle null time (when mouse is in whitespace area)
    if (newTime == null) {
        const visibleRange = chart.timeScale().getVisibleRange();
        if (visibleRange) {
            const chartWidth = document.getElementById("chart").clientWidth;
            const totalTimeRange = visibleRange.to - visibleRange.from;
            const mouseRatio = mouseX / chartWidth;
            newTime = visibleRange.from + (totalTimeRange * mouseRatio);
        } else {
            return; // Cannot calculate time, skip update
        }
    }
    
    // Update the appropriate endpoint
    if (endpoint === "start") {
        line.p1 = newPrice;
        line.t1 = newTime;
    } else {
        line.p2 = newPrice;
        line.t2 = newTime;
    }
    
    // Recreate the trendline with new endpoints
    if (line.trendlineSeries) {
        try {
            chart.removeSeries(line.trendlineSeries);
        } catch(e) {
            console.error("Error removing old trendline series:", e);
        }
    }
    
    // Create new trendline series with updated endpoints
    line.trendlineSeries = createTrendlineSeries(
        { time: line.t1, price: line.p1 },
        { time: line.t2, price: line.p2 },
        { color: "#ff6b6b", lineWidth: 2, title: "Trendline" }
    );
    
    // Update list item if it exists
    const listItem = document.getElementById("li-" + line.id);
    if (listItem) {
        const pill = listItem.querySelector(".pill");
        if (pill) {
            pill.textContent = `${fmt(line.p1)} → ${fmt(line.p2)}`;
        }
    }
    
    // Post update event
    post("lw_trendline_endpoint_moved", { 
        id: line.id, 
        endpoint: endpoint,
        newPrice: newPrice,
        newTime: newTime,
        p1: line.p1,
        p2: line.p2,
        t1: line.t1,
        t2: line.t2,
        group: line.group 
    });
}

// Function to update rectangle corner position during drag
function updateRectangleCornerDrag(mouseX, mouseY) {
    if (!draggedRectangleCorner) return;

    const { line, corner } = draggedRectangleCorner;

    // Calculate new price and time based on mouse position
    const newPrice = snap(series.coordinateToPrice(mouseY));
    if (isNaN(newPrice)) return;

    let newTime = chart.timeScale().coordinateToTime(mouseX);

    // Handle null time (when mouse is in whitespace area)
    if (newTime == null) {
        newTime = calculateTimeFromPosition(mouseX);
        if (!newTime) return;
    }

    // Update the appropriate corner based on which corner is being dragged
    switch(corner) {
        case "top-left":
            line.startTime = newTime;
            line.topPrice = newPrice;
            break;
        case "top-right":
            line.endTime = newTime;
            line.topPrice = newPrice;
            break;
        case "bottom-left":
            line.startTime = newTime;
            line.bottomPrice = newPrice;
            break;
        case "bottom-right":
            line.endTime = newTime;
            line.bottomPrice = newPrice;
            break;
    }

    // Recreate rectangle with new dimensions
    recreateRectangle(line);

    // Post update event
    post("lw_rectangle_corner_moved", {
        id: line.id,
        corner: corner,
        newPrice: newPrice,
        newTime: newTime,
        startTime: line.startTime,
        endTime: line.endTime,
        topPrice: line.topPrice,
        bottomPrice: line.bottomPrice,
        group: line.group
    });
}

// Function to update line position during drag (both price and time)
function updateLineDrag(mouseX, mouseY) {
    if (!isDragging || !draggedLine) return;
    
    // Calculate new price based on mouse Y position
    const newPrice = snap(series.coordinateToPrice(mouseY));
    if (isNaN(newPrice)) return;
    
    // Calculate new time based on mouse X position
    let newTime = chart.timeScale().coordinateToTime(mouseX);
    
    // Handle null time (when mouse is in whitespace area)
    if (newTime == null) {
        const visibleRange = chart.timeScale().getVisibleRange();
        if (visibleRange) {
            const chartWidth = document.getElementById("chart").clientWidth;
            const totalTimeRange = visibleRange.to - visibleRange.from;
            const mouseRatio = mouseX / chartWidth;
            newTime = visibleRange.from + (totalTimeRange * mouseRatio);
        } else {
            return; // Cannot calculate time, skip update
        }
    }
    
    // Update the line price and time
    const oldPrice = draggedLine.price;
    const oldStartTime = draggedLine.startTime;
    const oldEndTime = draggedLine.endTime;
    
    draggedLine.price = newPrice;
    
    // Update the line based on its type
    if (draggedLine.handle) {
        try {
            // For LineSeries-based lines (time-bounded lines), update both price and time
            const currentData = draggedLine.handle.data();
            if (currentData && currentData.length > 0) {
                // Calculate time shift
                const timeShift = newTime - (oldStartTime || currentData[0].time);
                
                const updatedData = currentData.map(point => ({
                    time: point.time + timeShift,
                    value: newPrice
                }));
                
                draggedLine.handle.setData(updatedData);
                
                // Update stored times
                if (draggedLine.startTime) draggedLine.startTime = newTime;
                if (draggedLine.endTime) draggedLine.endTime = draggedLine.endTime + timeShift;
            }
        } catch(e) {
            console.error("Error updating line data:", e);
        }
    }
    
    // Update list item if it exists
    const listItem = document.getElementById("li-" + draggedLine.id);
    if (listItem) {
        const pill = listItem.querySelector(".pill");
        if (pill) {
            pill.textContent = fmt(newPrice);
        }
    }
    
    // If this is part of a Fibonacci set, recalculate trade details
    if (draggedLine.group && (draggedLine.kind.startsWith("fib") || draggedLine.kind.startsWith("fibtp"))) {
        updateFibonacciTradeDetails(draggedLine.group);
    }
    
    // Post update event
    post("lw_line_moved", { 
        id: draggedLine.id, 
        oldPrice: oldPrice, 
        newPrice: newPrice,
        oldStartTime: oldStartTime,
        newStartTime: draggedLine.startTime,
        oldEndTime: oldEndTime,
        newEndTime: draggedLine.endTime,
        kind: draggedLine.kind,
        group: draggedLine.group 
    });
}

// Function to end dragging
function endDrag() {
    if (!isDragging) return;

    hideCornerDot();
    isDragging = false;
    draggedLine = null;
    draggedTrendlineEndpoint = null;
    draggedRectangleCorner = null;
    dragStartY = null;
    dragStartX = null;

    // Re-enable chart pan/zoom
    setPanZoomEnabled(true);

    // Reset cursor
    document.getElementById("chart").style.cursor = "default";
}

// Function to update Fibonacci trade details when a fib line is moved
function updateFibonacciTradeDetails(groupId) {
    const groupLines = lines.filter(l => l.group === groupId);
    const p1Line = groupLines.find(l => l.kind === "fib1");
    const p2Line = groupLines.find(l => l.kind === "fib2");
    const fib50Line = groupLines.find(l => l.kind === "fib50");
    const fib618Line = groupLines.find(l => l.kind === "fib618");
    const fib786Line = groupLines.find(l => l.kind === "fib786");
    const fibtp236Line = groupLines.find(l => l.kind === "fibtp236");
    const fibtp382Line = groupLines.find(l => l.kind === "fibtp382");
    
    if (p1Line && p2Line && typeof generateTradeDetails === "function") {
        generateTradeDetails(
            p1Line.price,
            p2Line.price,
            fib50Line ? fib50Line.price : null,
            fib618Line ? fib618Line.price : null,
            fib786Line ? fib786Line.price : null,
            fibtp236Line ? fibtp236Line.price : null,
            fibtp382Line ? fibtp382Line.price : null
        );
    }
}

let hoverCornerDot = null;

function showCornerDotAt(time, price) {
  if (!hoverCornerDot) {
    hoverCornerDot = chart.addLineSeries({
      color: '#58a6ff', lineWidth: 2,
      priceLineVisible: false, lastValueVisible: false, title: 'corner-dot'
    });
  }
  hoverCornerDot.setData([{ time, value: price }]);
}

function hideCornerDot() {
  if (hoverCornerDot) { try { chart.removeSeries(hoverCornerDot); } catch(_) {} hoverCornerDot = null; }
}

// Add mouse event listeners for enhanced dragging
document.addEventListener("DOMContentLoaded", function() {
    const chartContainer = document.getElementById("chart");
    
    // Mouse move for cursor updates and drag updates
    chartContainer.addEventListener("mousemove", function(e) {
        if (isDragging) {
            if (draggedRectangleCorner) {
                updateRectangleCornerDrag(e.offsetX, e.offsetY);
            } else if (draggedTrendlineEndpoint) {
                updateTrendlineEndpointDrag(e.offsetX, e.offsetY);
            } else if (draggedLine) {
                updateLineDrag(e.offsetX, e.offsetY);
            }
        } else {
            updateCursor(e.offsetX, e.offsetY);
        }
    });
    
    // Mouse down to start dragging
    chartContainer.addEventListener("mousedown", function(e) {
        // Only handle left mouse button
        if (e.button !== 0) return;
        
        // Try to start drag
        if (startDrag(e.offsetX, e.offsetY)) {
            e.preventDefault();
            e.stopPropagation();
        }
    });
    
    // Mouse up to end dragging
    document.addEventListener("mouseup", function(e) {
        if (isDragging) {
            endDrag();
            e.preventDefault();
            e.stopPropagation();
        }
    });
    
    // Mouse leave chart area - end dragging
    chartContainer.addEventListener("mouseleave", function() {
        if (isDragging) {
            endDrag();
        }
    });
});
