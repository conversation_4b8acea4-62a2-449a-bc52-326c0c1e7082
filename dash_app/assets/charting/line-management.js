// Line management - extracted from GOOD CHART.html
// This file contains line creation, removal, and list management functions

// ----- State & helpers -----
const pf = series.options().priceFormat || {};
const minMove = pf.minMove ?? 0.01;
const lines = []; // { id, price, handle, kind: 'single'|'fib1'|'fib2'|'fib50'|'fib618'|'fib786'|'fibtp236'|'fibtp618'|'rectangle'|'trendline', group?:string }
let lastPoint = null; // last crosshair point {x,y}
let mode = 'normal';    // 'normal' | 'line' | 'trendline' | 'fib' | 'rect'
const fmt = (x) => Number(x).toLocaleString(undefined, { maximumFractionDigits: 8 });

// For Rectangle mode
let rectActive = false;
let rectStartPoint = null; // {x, y, time, price}
let tempRectTop = null; // temp top line
let tempRectBottom = null; // temp bottom line
let tempRectLeft = null; // temp left vertical line series
let lastRectUpdate = 0; // throttle rectangle updates
let rectUpdateTimeout = null; // for final update when mouse stops

// For Trendline mode
let trendlineActive = false;
let trendlineStartPoint = null; // {x, y, time, price}
let tempTrendlineSeries = null;
let lastTrendlineUpdate = 0;

// For Fib mode
let fibActive = false;
let fibStartPrice = null;
let fibStartTime = null; // capture start time for time-bounded fibs
let tempStart = null; // temp line handles
let tempEnd = null;

function snap(price) {
  return Math.round(price / minMove) * minMove;
}

function createLine(price, opts = {}) {
  const handle = series.createPriceLine({
    price,
    color: opts.color || '#58a6ff',
    lineWidth: opts.lineWidth ?? 2,
    lineStyle: opts.lineStyle ?? 0, // 0=solid, 1=dotted, 2=dashed
    axisLabelVisible: true,
    title: opts.title ?? String(price),
  });
  const id = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
  const rec = { id, price, handle, kind: opts.kind || 'single', group: opts.group };
  lines.push(rec);

  // Only add list item for single lines, fib sets, rectangles, and trendlines (not individual fib/rect levels)
  if (opts.kind === 'single' || opts.kind === 'fibset' || opts.kind === 'rectangle' || opts.kind === 'trendline') {
    addListItem(rec);
  }

  post('lw_line_added', { price, kind: rec.kind, group: rec.group });
  return rec;
}

// Create time-bounded Fibonacci level (horizontal line between two time points)
function createFibLevel(price, startTime, endTime, opts = {}) {
  const fibSeries = chart.addLineSeries({
    color: opts.color || '#58a6ff',
    lineWidth: opts.lineWidth ?? 2,
    lineStyle: opts.lineStyle ?? 0, // 0=solid, 1=dotted, 2=dashed
    priceLineVisible: false,
    lastValueVisible: false,
    title: opts.title || `Fib ${price.toFixed(2)}`
  });

  // Create horizontal line data from start time to end time (or future)
  const currentData = series.data();
  const lastTime = currentData && currentData.length > 0 ?
    currentData[currentData.length - 1].time : endTime;
  const fibEndTime = Math.max(endTime, lastTime + (3600 * 24 * 7)); // Extend 7 days into future

  fibSeries.setData([
    { time: startTime, value: price },
    { time: fibEndTime, value: price }
  ]);

  const id = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
  const rec = {
    id,
    price,
    handle: fibSeries,
    kind: opts.kind || 'fibLevel',
    group: opts.group,
    startTime,
    endTime: fibEndTime
  };
  lines.push(rec);

  // Add list item for single lines, but not for individual fib levels
  if (opts.kind === 'single') {
    addListItem(rec);
  }

  post('lw_line_added', { price, kind: rec.kind, group: rec.group });
  return rec;
}

// Create a proper rectangle using LineSeries
function createRectangle(startTime, endTime, topPrice, bottomPrice, opts = {}) {
  const rectSeries = chart.addLineSeries({
    color: opts.color || '#58a6ff',
    lineWidth: opts.lineWidth ?? 2,
    lineStyle: opts.lineStyle ?? 0, // 0=solid, 1=dotted, 2=dashed
    priceLineVisible: false,
    lastValueVisible: false,
    crosshairMarkerVisible: false, // disable built-in corner dot so we control dots ourselves
    title: opts.title || 'Rectangle'
  });

  // Create rectangle outline data points
  const rectangleData = [
    { time: startTime, value: topPrice },    // Top-left
    { time: endTime, value: topPrice },      // Top-right
    { time: endTime, value: bottomPrice },   // Bottom-right
    { time: startTime, value: bottomPrice }, // Bottom-left
    { time: startTime, value: topPrice }     // Close the rectangle
  ];

  rectSeries.setData(rectangleData);

  const id = (crypto?.randomUUID?.() || Math.random().toString(36).slice(2));
  const rec = {
    id,
    handle: rectSeries,
    kind: 'rectangle',
    group: opts.group,
    startTime,
    endTime,
    topPrice,
    bottomPrice
  };
  lines.push(rec);
  addListItem(rec);

  post('lw_line_added', { kind: rec.kind, group: rec.group });
  return rec;
}

function removeLineById(id) {
  const idx = lines.findIndex(l => l.id === id);
  if (idx === -1) return;
  const rec = lines[idx];

  // Handle different types of chart elements
  if (rec.handle) {
    // Check if it's a LineSeries-based element (all Fibonacci levels, rectangles, and single lines)
    const isLineSeries = rec.kind === 'fibLevel' || rec.kind === 'fib1' || rec.kind === 'fib2' ||
                        rec.kind === 'fib50' || rec.kind === 'fib618' || rec.kind === 'fib786' ||
                        rec.kind === 'fibtp236' || rec.kind === 'fibtp382' || rec.kind === 'single' ||
                        rec.kind === 'rectangle';

    if (isLineSeries) {
      // Remove LineSeries-based elements
      try { chart.removeSeries(rec.handle); } catch(e){}
    } else {
      // Remove price line (trendlines don't have handles, they're managed by group removal)
      try { series.removePriceLine(rec.handle); } catch(e){}
    }
  }

  // Handle trendline series removal
  if (rec.kind === 'trendline' && rec.trendlineSeries) {
    try { chart.removeSeries(rec.trendlineSeries); } catch(e){}
  }

  lines.splice(idx, 1);
  const el = document.getElementById('li-' + id);
  if (el) el.remove();
  post('lw_line_removed', { id, kind: rec.kind, group: rec.group });
}

function removeGroup(groupId) {
  const groupLines = lines.filter(l => l.group === groupId);

  // Check if this group contains a Fibonacci set
  const fibInGroup = groupLines.find(l => l.kind === 'fibset' ||
    l.kind === 'fib1' || l.kind === 'fib2' || l.kind === 'fib50' ||
    l.kind === 'fib618' || l.kind === 'fib786' || l.kind === 'fibtp236' || l.kind === 'fibtp382');

  if (fibInGroup) {
    // Clear trade details when removing a Fibonacci set
    tradeInfoEl.innerHTML = '<div class="muted">Draw a Fibonacci retracement to generate trade plan</div>';
  }

  // Handle rectangle markers removal
  const rectangleInGroup = groupLines.find(l => l.kind === 'rectangle');
  if (rectangleInGroup && rectangleInGroup.markers) {
    try {
      // Get existing markers and filter out the rectangle's markers
      const existingMarkers = series.markers?.() || [];
      const filteredMarkers = existingMarkers.filter(marker =>
        !rectangleInGroup.markers.some(rectMarker =>
          rectMarker.time === marker.time && rectMarker.text === marker.text
        )
      );
      series.setMarkers(filteredMarkers);
    } catch(e) {
      console.log('Error removing rectangle markers:', e);
    }
  }

  // Handle trendline series removal
  const trendlineInGroup = groupLines.find(l => l.kind === 'trendline');
  if (trendlineInGroup && trendlineInGroup.trendlineSeries) {
    try {
      chart.removeSeries(trendlineInGroup.trendlineSeries);
    } catch(e) {
      console.log('Error removing trendline series:', e);
    }
  }

  const toRemove = groupLines.map(l => l.id);
  toRemove.forEach(removeLineById);
}

function addListItem(rec) {
  const li = document.createElement('li');
  li.className = 'chart-list-item';
  li.id = 'li-' + rec.id;

  const left = document.createElement('div');
  if (rec.kind === 'fibset') {
    left.textContent = 'Fib Set ';
    const pill = document.createElement('span');
    pill.className='pill';
    pill.textContent = `P1: ${fmt(rec.p1)} → P2: ${fmt(rec.p2)}`;
    left.appendChild(pill);
  } else if (rec.kind === 'rectangle') {
    left.textContent = 'Rectangle ';
    const pill = document.createElement('span');
    pill.className='pill';
    pill.textContent = `${fmt(rec.p1)} → ${fmt(rec.p2)}`;
    left.appendChild(pill);
  } else if (rec.kind === 'trendline') {
    left.textContent = 'Trendline ';
    const pill = document.createElement('span');
    pill.className='pill';
    pill.textContent = `${fmt(rec.p1)} → ${fmt(rec.p2)}`;
    left.appendChild(pill);
  } else {
    left.textContent = (rec.kind === 'single') ? 'Line @ ' : 'Line @ ';
    const pill = document.createElement('span'); pill.className='pill'; pill.textContent = fmt(rec.price);
    left.appendChild(pill);
  }

  if (rec.group && (rec.kind === 'fibset' || rec.kind === 'rectangle' || rec.kind === 'trendline')) {
    const tag = document.createElement('span');
    tag.className = 'groupTag';
    tag.textContent = rec.group.slice(0,6);
    left.appendChild(tag);
  }

  const right = document.createElement('div');

  if (rec.kind === 'fibset' || rec.kind === 'rectangle' || rec.kind === 'trendline') {
    // For fib sets, rectangles, and trendlines, only show delete button (no copy since it's multiple values)
    const del = document.createElement('button'); del.className='chart-btn'; del.textContent='🗑️';
    del.onclick = () => removeGroup(rec.group);
    right.appendChild(del);
  } else {
    // For single lines, show both copy and delete
    const copy = document.createElement('button'); copy.className='chart-btn'; copy.textContent='Copy';
    copy.onclick = () => { navigator.clipboard.writeText(String(rec.price)); copy.textContent='✔'; setTimeout(()=>copy.textContent='Copy',800); };

    const del = document.createElement('button'); del.className='chart-btn'; del.textContent='🗑️';
    del.onclick = () => removeLineById(rec.id);

    right.append(copy, del);
  }

  li.append(left, right);
  listEl.appendChild(li);
}

function post(type, payload) {
  try { window.parent?.postMessage({ type, ...payload }, '*'); } catch(e){}
}

// Helper function to calculate trendline price at a given time
function calculateTrendlinePrice(startTime, startPrice, endTime, endPrice, targetTime) {
  if (startTime === endTime) return startPrice; // Avoid division by zero
  const slope = (endPrice - startPrice) / (endTime - startTime);
  return startPrice + slope * (targetTime - startTime);
}

// Helper function to create trendline series
function createTrendlineSeries(startPoint, endPoint, opts = {}) {
  // Validate input points
  if (!startPoint || !endPoint ||
      startPoint.time == null || endPoint.time == null ||
      startPoint.price == null || endPoint.price == null) {
    console.error('Invalid trendline points:', { startPoint, endPoint });
    return null;
  }

  const trendlineSeries = chart.addLineSeries({
    color: opts.color || '#ff6b6b',
    lineWidth: opts.lineWidth || 2,
    lineStyle: opts.lineStyle || 0, // solid
    lastValueVisible: false,
    priceLineVisible: false,
    title: opts.title || 'Trendline'
  });

  // Ensure times are valid numbers (Unix timestamps)
  const startTime = typeof startPoint.time === 'number' ? startPoint.time : Math.floor(startPoint.time);
  const endTime = typeof endPoint.time === 'number' ? endPoint.time : Math.floor(endPoint.time);

  // Create trendline data points including future projection
  const trendlineData = [
    { time: startTime, value: startPoint.price },
    { time: endTime, value: endPoint.price }
  ];

  // No automatic future projection - user controls trendline endpoints

  // Set the trendline data with future projection
  try {
    trendlineSeries.setData(trendlineData);
  } catch (error) {
    console.error('Error setting trendline data:', error, { startTime, endTime, startPrice: startPoint.price, endPrice: endPoint.price });
    chart.removeSeries(trendlineSeries);
    return null;
  }

  return trendlineSeries;
}


