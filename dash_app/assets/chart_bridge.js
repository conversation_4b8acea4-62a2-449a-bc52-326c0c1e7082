/**
 * Chart Bridge - JavaScript bridge between Dash components and LightweightCharts
 * This file handles the communication between Dash chart controls and the LightweightCharts instance
 */

// Global chart management
window.chartInstances = window.chartInstances || {};
window.chartBridge = window.chartBridge || {};

/**
 * Initialize a new chart instance
 * @param {string} containerId - The ID of the container element
 * @param {string} chartId - Unique identifier for this chart instance
 */
window.chartBridge.initChart = function(containerId, chartId) {
    const container = document.getElementById(containerId);
    if (!container) {
        console.error(`Chart container ${containerId} not found`);
        return null;
    }

    // Clean up existing chart if it exists
    if (window.chartInstances[chartId]) {
        try {
            window.chartInstances[chartId].chart.remove();
        } catch (e) {
            console.warn('Error removing existing chart:', e);
        }
    }

    // Create new chart
    const chart = LightweightCharts.createChart(container, {
        layout: { 
            background: { color: '#0e1116' }, 
            textColor: '#c9d1d9' 
        },
        rightPriceScale: {
            autoScale: true,
        },
        timeScale: { 
            borderVisible: false, 
            timeVisible: true, 
            secondsVisible: false 
        },
        grid: { 
            horzLines: { color: '#1f2633' }, 
            vertLines: { color: '#1f2633' } 
        },
        crosshair: { 
            mode: LightweightCharts.CrosshairMode.Normal 
        },
    });

    // Create initial candlestick series
    const series = chart.addCandlestickSeries({
        upColor: '#26a69a',
        downColor: '#ef5350',
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
        borderVisible: false,
        priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
    });

    // Store chart instance
    window.chartInstances[chartId] = {
        chart: chart,
        series: series,
        currentChartType: 'candlestick',
        currentData: null
    };

    // Handle resize
    const resizeObserver = new ResizeObserver(entries => {
        const { width, height } = entries[0].contentRect;
        chart.applyOptions({ width, height });
    });
    resizeObserver.observe(container);

    console.log(`Chart ${chartId} initialized in container ${containerId}`);
    return window.chartInstances[chartId];
};

/**
 * Update chart data
 * @param {string} chartId - Chart instance identifier
 * @param {Array} data - Array of candlestick data points
 * @param {string} chartType - 'candlestick' or 'line'
 */
window.chartBridge.updateChartData = function(chartId, data, chartType = 'candlestick') {
    const chartInstance = window.chartInstances[chartId];
    if (!chartInstance) {
        console.error(`Chart instance ${chartId} not found`);
        return;
    }

    const { chart, series } = chartInstance;

    try {
        // Store the data
        chartInstance.currentData = data;

        // Update chart type if needed
        if (chartInstance.currentChartType !== chartType) {
            window.chartBridge.switchChartType(chartId, chartType);
            return; // switchChartType will handle the data update
        }

        // Update data based on chart type
        if (chartType === 'line') {
            // Convert candlestick data to line data (using close prices)
            const lineData = data.map(candle => ({
                time: candle.time,
                value: candle.close
            }));
            series.setData(lineData);
        } else {
            // Use candlestick data directly
            series.setData(data);
        }

        // Set visible range to show recent data with some future space
        if (data && data.length > 0) {
            const lastTime = data[data.length - 1].time;
            const timeInterval = data.length > 1 ? data[1].time - data[0].time : 3600;
            const futureTime = lastTime + (timeInterval * 7);
            
            chart.timeScale().setVisibleRange({
                from: data[Math.max(0, data.length - 100)].time,
                to: futureTime
            });
        }

        console.log(`Chart ${chartId} updated with ${data.length} data points (${chartType})`);
    } catch (error) {
        console.error(`Error updating chart ${chartId}:`, error);
    }
};

/**
 * Switch chart type between candlestick and line
 * @param {string} chartId - Chart instance identifier
 * @param {string} chartType - 'candlestick' or 'line'
 */
window.chartBridge.switchChartType = function(chartId, chartType) {
    const chartInstance = window.chartInstances[chartId];
    if (!chartInstance) {
        console.error(`Chart instance ${chartId} not found`);
        return;
    }

    const { chart, currentData } = chartInstance;

    try {
        // Remove existing series
        chart.removeSeries(chartInstance.series);

        // Create new series based on type
        if (chartType === 'line') {
            chartInstance.series = chart.addLineSeries({
                color: '#58a6ff',
                lineWidth: 2,
                priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
            });

            // Convert candlestick data to line data if available
            if (currentData) {
                const lineData = currentData.map(candle => ({
                    time: candle.time,
                    value: candle.close
                }));
                chartInstance.series.setData(lineData);
            }
        } else {
            chartInstance.series = chart.addCandlestickSeries({
                upColor: '#26a69a',
                downColor: '#ef5350',
                wickUpColor: '#26a69a',
                wickDownColor: '#ef5350',
                borderVisible: false,
                priceFormat: { type: 'price', precision: 2, minMove: 0.01 },
            });

            // Use stored candlestick data if available
            if (currentData) {
                chartInstance.series.setData(currentData);
            }
        }

        chartInstance.currentChartType = chartType;
        console.log(`Chart ${chartId} switched to ${chartType} type`);
    } catch (error) {
        console.error(`Error switching chart type for ${chartId}:`, error);
    }
};

/**
 * Get chart instance
 * @param {string} chartId - Chart instance identifier
 * @returns {Object|null} Chart instance or null if not found
 */
window.chartBridge.getChart = function(chartId) {
    return window.chartInstances[chartId] || null;
};

/**
 * Remove chart instance
 * @param {string} chartId - Chart instance identifier
 */
window.chartBridge.removeChart = function(chartId) {
    const chartInstance = window.chartInstances[chartId];
    if (chartInstance) {
        try {
            chartInstance.chart.remove();
            delete window.chartInstances[chartId];
            console.log(`Chart ${chartId} removed`);
        } catch (error) {
            console.error(`Error removing chart ${chartId}:`, error);
        }
    }
};

// Auto-initialize charts when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    console.log('Chart bridge loaded and ready');
});

console.log('Chart bridge script loaded');
