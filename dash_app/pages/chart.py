import dash
from dash import html, dcc
from flask import session

from services import binance as binance_service
from dash_app.widgets.chart_controls import create_chart_controls, get_callbacks

dash.register_page(__name__, name="TradeCraft - Chart")

# Layout: Use the new Dash chart controls widget
controls_layout = html.Div([
    # Include LightweightCharts library
    html.Script(src="https://unpkg.com/lightweight-charts@4.2.2/dist/lightweight-charts.standalone.production.js"),

    # Include our chart bridge JavaScript
    html.Script(src="/assets/chart_bridge.js"),

    # Chart controls widget
    create_chart_controls("main-chart")

], style={
    "padding": "0",
    "margin": "0",
    "width": "100%",
    "height": "100vh",
    "backgroundColor": "#0e1116"
})


def layout():
    if "user_id" not in session:
        return dcc.Location(pathname="/login", id="redirect")
    return controls_layout


# Register callbacks for the chart controls
def register_chart_callbacks(app):
    """Register chart control callbacks with the app"""
    get_callbacks(app, "main-chart")
